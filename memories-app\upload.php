<?php
session_start();
header('Content-Type: application/json');
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    $description = trim($_POST['description'] ?? '');
    
    // Create uploads directory if it doesn't exist
    $upload_dir = 'uploads/memories/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Check if files were uploaded
    if (!isset($_FILES['images']) || empty($_FILES['images']['name'][0])) {
        echo json_encode(['success' => false, 'message' => 'يرجى اختيار صورة واحدة على الأقل']);
        exit();
    }
    
    $uploaded_files = [];
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $max_file_size = 5 * 1024 * 1024; // 5MB
    
    // Process each uploaded file
    $files = $_FILES['images'];
    $file_count = count($files['name']);
    
    for ($i = 0; $i < $file_count; $i++) {
        if ($files['error'][$i] !== UPLOAD_ERR_OK) {
            continue;
        }
        
        $file_name = $files['name'][$i];
        $file_tmp = $files['tmp_name'][$i];
        $file_size = $files['size'][$i];
        $file_type = $files['type'][$i];
        
        // Validate file type
        if (!in_array($file_type, $allowed_types)) {
            echo json_encode(['success' => false, 'message' => 'نوع الملف غير مدعوم: ' . $file_name]);
            exit();
        }
        
        // Validate file size
        if ($file_size > $max_file_size) {
            echo json_encode(['success' => false, 'message' => 'حجم الملف كبير جداً: ' . $file_name]);
            exit();
        }
        
        // Generate unique filename
        $file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
        $unique_name = uniqid('memory_' . $user_id . '_') . '.' . $file_extension;
        $file_path = $upload_dir . $unique_name;
        
        // Move uploaded file
        if (move_uploaded_file($file_tmp, $file_path)) {
            // Resize image if needed
            resizeImage($file_path, 1200, 800);
            
            // Insert into database
            $stmt = $db->prepare("INSERT INTO memories (user_id, description, image_path) VALUES (?, ?, ?)");
            if ($stmt->execute([$user_id, $description, $file_path])) {
                $uploaded_files[] = [
                    'id' => $db->lastInsertId(),
                    'path' => $file_path,
                    'name' => $file_name
                ];
            } else {
                // Delete file if database insert failed
                unlink($file_path);
                echo json_encode(['success' => false, 'message' => 'خطأ في حفظ الملف: ' . $file_name]);
                exit();
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في رفع الملف: ' . $file_name]);
            exit();
        }
    }
    
    if (empty($uploaded_files)) {
        echo json_encode(['success' => false, 'message' => 'لم يتم رفع أي ملف']);
    } else {
        echo json_encode([
            'success' => true, 
            'message' => 'تم رفع ' . count($uploaded_files) . ' ملف بنجاح',
            'files' => $uploaded_files
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
    error_log($e->getMessage());
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ غير متوقع']);
    error_log($e->getMessage());
}

// Function to resize image
function resizeImage($file_path, $max_width, $max_height) {
    $image_info = getimagesize($file_path);
    if (!$image_info) return false;
    
    $width = $image_info[0];
    $height = $image_info[1];
    $type = $image_info[2];
    
    // Check if resize is needed
    if ($width <= $max_width && $height <= $max_height) {
        return true;
    }
    
    // Calculate new dimensions
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = round($width * $ratio);
    $new_height = round($height * $ratio);
    
    // Create image resource based on type
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($file_path);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($file_path);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($file_path);
            break;
        case IMAGETYPE_WEBP:
            $source = imagecreatefromwebp($file_path);
            break;
        default:
            return false;
    }
    
    if (!$source) return false;
    
    // Create new image
    $destination = imagecreatetruecolor($new_width, $new_height);
    
    // Preserve transparency for PNG and GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($destination, false);
        imagesavealpha($destination, true);
        $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
        imagefilledrectangle($destination, 0, 0, $new_width, $new_height, $transparent);
    }
    
    // Resize image
    imagecopyresampled($destination, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
    
    // Save resized image
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($destination, $file_path, 85);
            break;
        case IMAGETYPE_PNG:
            imagepng($destination, $file_path, 8);
            break;
        case IMAGETYPE_GIF:
            imagegif($destination, $file_path);
            break;
        case IMAGETYPE_WEBP:
            imagewebp($destination, $file_path, 85);
            break;
    }
    
    // Clean up
    imagedestroy($source);
    imagedestroy($destination);
    
    return true;
}
?>
