<?php
session_start();
header('Content-Type: application/json');
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $memory_id = intval($input['memory_id'] ?? 0);
    $user_id = $_SESSION['user_id'];
    
    if ($memory_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'معرف الذكرى غير صحيح']);
        exit();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if memory exists
    $stmt = $db->prepare("SELECT id FROM memories WHERE id = ?");
    $stmt->execute([$memory_id]);
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'الذكرى غير موجودة']);
        exit();
    }
    
    // Check if user already liked this memory
    $stmt = $db->prepare("SELECT id FROM likes WHERE memory_id = ? AND user_id = ?");
    $stmt->execute([$memory_id, $user_id]);
    $existing_like = $stmt->fetch();
    
    if ($existing_like) {
        // Remove like
        $stmt = $db->prepare("DELETE FROM likes WHERE memory_id = ? AND user_id = ?");
        $stmt->execute([$memory_id, $user_id]);
        $liked = false;
    } else {
        // Add like
        $stmt = $db->prepare("INSERT INTO likes (memory_id, user_id) VALUES (?, ?)");
        $stmt->execute([$memory_id, $user_id]);
        $liked = true;
    }
    
    // Get updated likes count
    $stmt = $db->prepare("SELECT COUNT(*) as likes_count FROM likes WHERE memory_id = ?");
    $stmt->execute([$memory_id]);
    $likes_count = $stmt->fetch()['likes_count'];
    
    echo json_encode([
        'success' => true,
        'liked' => $liked,
        'likes_count' => intval($likes_count)
    ]);
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
    error_log($e->getMessage());
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ غير متوقع']);
    error_log($e->getMessage());
}
?>
