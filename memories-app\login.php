<?php
session_start();
require_once 'config/database.php';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = 'اسم المستخدم وكلمة المرور مطلوبان';
    } else {
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            $stmt = $db->prepare("SELECT id, username, password FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                
                // Update last login
                $stmt = $db->prepare("UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$user['id']]);
                
                header('Location: index.php');
                exit();
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch (PDOException $e) {
            $error = 'خطأ في قاعدة البيانات';
            error_log($e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - تطبيق الذكريات</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-camera-retro"></i>
                    <h1>تطبيق الذكريات</h1>
                </div>
                <h2>تسجيل الدخول</h2>
                <p>أهلاً بك مرة أخرى! سجل دخولك لمشاهدة ذكرياتك</p>
            </div>
            
            <div class="auth-form">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <div class="form-group">
                        <label for="username">
                            <i class="fas fa-user"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" id="username" name="username" required 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                               placeholder="أدخل اسم المستخدم">
                    </div>
                    
                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <input type="password" id="password" name="password" required 
                               placeholder="أدخل كلمة المرور">
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" name="remember_me">
                            <span class="checkmark"></span>
                            تذكرني
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </button>
                </form>
                
                <!-- Demo Account Info -->
                <div class="demo-info">
                    <h4><i class="fas fa-info-circle"></i> حساب تجريبي</h4>
                    <p>يمكنك استخدام الحساب التجريبي للتجربة:</p>
                    <div class="demo-credentials">
                        <button type="button" class="demo-btn" onclick="fillDemoCredentials()">
                            <i class="fas fa-user-cog"></i>
                            استخدام الحساب التجريبي
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="auth-footer">
                <p>ليس لديك حساب؟ <a href="register.php">إنشاء حساب جديد</a></p>
                <p><a href="index.php">العودة للصفحة الرئيسية</a></p>
            </div>
        </div>
        
        <!-- Decorative Elements -->
        <div class="auth-decoration">
            <div class="floating-icon" style="top: 15%; left: 10%;">
                <i class="fas fa-heart"></i>
            </div>
            <div class="floating-icon" style="top: 25%; right: 15%;">
                <i class="fas fa-camera"></i>
            </div>
            <div class="floating-icon" style="bottom: 35%; left: 20%;">
                <i class="fas fa-star"></i>
            </div>
            <div class="floating-icon" style="bottom: 25%; right: 10%;">
                <i class="fas fa-gift"></i>
            </div>
            <div class="floating-icon" style="top: 50%; left: 5%;">
                <i class="fas fa-birthday-cake"></i>
            </div>
            <div class="floating-icon" style="top: 60%; right: 5%;">
                <i class="fas fa-balloon"></i>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Floating animation for decorative icons
            const floatingIcons = document.querySelectorAll('.floating-icon');
            floatingIcons.forEach((icon, index) => {
                icon.style.animationDelay = `${index * 0.3}s`;
            });
            
            // Form validation feedback
            const form = document.querySelector('form');
            const inputs = form.querySelectorAll('input[type="text"], input[type="password"]');
            
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() !== '') {
                        this.classList.add('has-value');
                    } else {
                        this.classList.remove('has-value');
                    }
                });
                
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });
        });
        
        // Fill demo credentials
        function fillDemoCredentials() {
            document.getElementById('username').value = 'demo_user';
            document.getElementById('password').value = 'demo123';
            
            // Add visual feedback
            const demoBtn = document.querySelector('.demo-btn');
            const originalText = demoBtn.innerHTML;
            demoBtn.innerHTML = '<i class="fas fa-check"></i> تم ملء البيانات';
            demoBtn.style.background = '#28a745';
            
            setTimeout(() => {
                demoBtn.innerHTML = originalText;
                demoBtn.style.background = '';
            }, 2000);
        }
        
        // Auto-create demo user if it doesn't exist
        fetch('api/create_demo_user.php', {
            method: 'POST'
        }).catch(error => {
            console.log('Demo user creation skipped');
        });
    </script>
</body>
</html>
