<?php
session_start();
header('Content-Type: application/json');
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $memory_id = intval($input['memory_id'] ?? 0);
    $comment_text = trim($input['comment'] ?? '');
    $user_id = $_SESSION['user_id'];
    
    if ($memory_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'معرف الذكرى غير صحيح']);
        exit();
    }
    
    if (empty($comment_text)) {
        echo json_encode(['success' => false, 'message' => 'نص التعليق مطلوب']);
        exit();
    }
    
    if (strlen($comment_text) > 1000) {
        echo json_encode(['success' => false, 'message' => 'التعليق طويل جداً']);
        exit();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if memory exists
    $stmt = $db->prepare("SELECT id FROM memories WHERE id = ?");
    $stmt->execute([$memory_id]);
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'الذكرى غير موجودة']);
        exit();
    }
    
    // Add comment
    $stmt = $db->prepare("INSERT INTO comments (memory_id, user_id, comment) VALUES (?, ?, ?)");
    if ($stmt->execute([$memory_id, $user_id, $comment_text])) {
        $comment_id = $db->lastInsertId();
        
        // Get the new comment with user info
        $stmt = $db->prepare("
            SELECT 
                c.id,
                c.comment,
                c.created_at,
                u.username,
                u.avatar
            FROM comments c
            JOIN users u ON c.user_id = u.id
            WHERE c.id = ?
        ");
        $stmt->execute([$comment_id]);
        $new_comment = $stmt->fetch();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة التعليق بنجاح',
            'comment' => [
                'id' => $new_comment['id'],
                'comment' => $new_comment['comment'],
                'created_at' => $new_comment['created_at'],
                'username' => $new_comment['username'],
                'avatar' => $new_comment['avatar']
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في إضافة التعليق']);
    }
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
    error_log($e->getMessage());
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ غير متوقع']);
    error_log($e->getMessage());
}
?>
