<?php
session_start();
header('Content-Type: application/json');
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

try {
    $memory_id = intval($_GET['memory_id'] ?? 0);
    
    if ($memory_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'معرف الذكرى غير صحيح']);
        exit();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if memory exists
    $stmt = $db->prepare("SELECT id FROM memories WHERE id = ?");
    $stmt->execute([$memory_id]);
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'الذكرى غير موجودة']);
        exit();
    }
    
    // Get comments with user info
    $sql = "
        SELECT 
            c.id,
            c.comment,
            c.created_at,
            u.username,
            u.avatar
        FROM comments c
        JOIN users u ON c.user_id = u.id
        WHERE c.memory_id = ?
        ORDER BY c.created_at ASC
    ";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$memory_id]);
    $comments = $stmt->fetchAll();
    
    // Process comments data
    $processed_comments = [];
    foreach ($comments as $comment) {
        $processed_comments[] = [
            'id' => $comment['id'],
            'comment' => $comment['comment'],
            'created_at' => $comment['created_at'],
            'username' => $comment['username'],
            'avatar' => $comment['avatar']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'comments' => $processed_comments
    ]);
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
    error_log($e->getMessage());
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ غير متوقع']);
    error_log($e->getMessage());
}
?>
