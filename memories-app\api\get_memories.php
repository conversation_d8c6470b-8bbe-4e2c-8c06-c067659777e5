<?php
session_start();
header('Content-Type: application/json');
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = 12; // Number of memories per page
    $offset = ($page - 1) * $limit;
    
    // Get memories with user info, likes count, and comments count
    $sql = "
        SELECT 
            m.id,
            m.user_id,
            m.title,
            m.description,
            m.image_path,
            m.created_at,
            u.username,
            u.avatar,
            COUNT(DISTINCT l.id) as likes_count,
            COUNT(DISTINCT c.id) as comments_count,
            MAX(CASE WHEN l.user_id = ? THEN 1 ELSE 0 END) as user_liked
        FROM memories m
        JOIN users u ON m.user_id = u.id
        LEFT JOIN likes l ON m.id = l.memory_id
        LEFT JOIN comments c ON m.id = c.memory_id
        GROUP BY m.id, m.user_id, m.title, m.description, m.image_path, m.created_at, u.username, u.avatar
        ORDER BY m.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id, $limit, $offset]);
    $memories = $stmt->fetchAll();
    
    // Get total count for pagination
    $count_sql = "SELECT COUNT(*) as total FROM memories";
    $count_stmt = $db->prepare($count_sql);
    $count_stmt->execute();
    $total_memories = $count_stmt->fetch()['total'];
    
    // Process memories data
    $processed_memories = [];
    foreach ($memories as $memory) {
        $processed_memories[] = [
            'id' => $memory['id'],
            'user_id' => $memory['user_id'],
            'title' => $memory['title'],
            'description' => $memory['description'],
            'image_path' => $memory['image_path'],
            'created_at' => $memory['created_at'],
            'username' => $memory['username'],
            'avatar' => $memory['avatar'],
            'likes_count' => intval($memory['likes_count']),
            'comments_count' => intval($memory['comments_count']),
            'user_liked' => intval($memory['user_liked']) === 1
        ];
    }
    
    echo json_encode([
        'success' => true,
        'memories' => $processed_memories,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => ceil($total_memories / $limit),
            'total_memories' => $total_memories,
            'has_next' => $page < ceil($total_memories / $limit),
            'has_prev' => $page > 1
        ]
    ]);
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
    error_log($e->getMessage());
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ غير متوقع']);
    error_log($e->getMessage());
}
?>
