<?php
header('Content-Type: application/json');
require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if demo user already exists
    $stmt = $db->prepare("SELECT id FROM users WHERE username = 'demo_user'");
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        // Create demo user
        $hashed_password = password_hash('demo123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO users (username, password, email) VALUES (?, ?, ?)");
        $stmt->execute(['demo_user', $hashed_password, '<EMAIL>']);
        
        echo json_encode(['success' => true, 'message' => 'Demo user created']);
    } else {
        echo json_encode(['success' => true, 'message' => 'Demo user already exists']);
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database error']);
    error_log($e->getMessage());
}
?>
