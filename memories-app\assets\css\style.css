/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', '<PERSON><PERSON>', serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
    position: relative;
}

/* Animated background elements */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 3px solid #d4af37;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #2c3e50;
}

.logo i {
    font-size: 2.5rem;
    color: #d4af37;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.logo h1 {
    font-family: 'Amiri', serif;
    font-size: 2rem;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    padding: 10px 20px;
    border-radius: 50px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.user-avatar i {
    font-size: 2rem;
    color: #d4af37;
}

.username {
    font-weight: 600;
    color: #2c3e50;
}

.logout-btn {
    color: #dc3545;
    text-decoration: none;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: rgba(220, 53, 69, 0.1);
    transform: scale(1.1);
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: inherit;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #d4af37, #f1c40f);
    color: white;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.btn-upload {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    width: 100%;
    margin-top: 15px;
}

.btn-upload:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.auth-buttons {
    display: flex;
    gap: 10px;
}

/* Main Content */
.main-content {
    padding: 2rem 0;
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    padding: 3rem 0;
}

.welcome-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid #d4af37;
    max-width: 600px;
    margin: 0 auto;
}

.welcome-icon i {
    font-size: 4rem;
    color: #d4af37;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-card h2 {
    font-family: 'Amiri', serif;
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.welcome-card p {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.feature {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 1rem;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.feature i {
    font-size: 1.5rem;
    color: #d4af37;
}

/* Upload Section */
.upload-section {
    margin-bottom: 3rem;
}

.upload-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid #d4af37;
}

.upload-card h2 {
    font-family: 'Amiri', serif;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    text-align: center;
}

.upload-area {
    border: 3px dashed #d4af37;
    border-radius: 15px;
    padding: 3rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #f8f9fa, #ffffff);
}

.upload-area:hover {
    border-color: #f1c40f;
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    transform: scale(1.02);
}

.upload-area i {
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 1rem;
}

.upload-area p {
    font-size: 1.2rem;
    color: #6c757d;
}

.form-group {
    margin-top: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
    transition: border-color 0.3s ease;
}

.form-group textarea:focus {
    outline: none;
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Gallery Section */
.gallery-section h2 {
    font-family: 'Amiri', serif;
    color: white;
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.memories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

/* QR Section */
.qr-section {
    text-align: center;
    padding: 2rem 0;
    position: relative;
}

.qr-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid #d4af37;
    max-width: 400px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    animation: pulse 3s ease-in-out infinite;
}

.qr-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #d4af37, #f1c40f, #d4af37);
    border-radius: 20px;
    z-index: -1;
    animation: borderGlow 2s ease-in-out infinite alternate;
}

.qr-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-family: 'Amiri', serif;
    font-size: 1.5rem;
}

.qr-card h3 i {
    color: #d4af37;
    margin-left: 10px;
}

.qr-code {
    margin: 1.5rem 0;
    display: flex;
    justify-content: center;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.qr-code canvas {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.qr-card p {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 1rem;
}

.qr-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 1rem;
}

.qr-btn {
    background: linear-gradient(45d, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.qr-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

@keyframes borderGlow {
    0% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Footer */
.footer {
    background: rgba(44, 62, 80, 0.9);
    color: white;
    text-align: center;
    padding: 1.5rem 0;
    margin-top: 3rem;
}

/* Memory Card Styles */
.memory-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(212, 175, 55, 0.2);
    transition: all 0.3s ease;
    animation: fadeInUp 0.6s ease-out;
}

.memory-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(212, 175, 55, 0.4);
}

.memory-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.memory-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.memory-card:hover .memory-image img {
    transform: scale(1.05);
}

.memory-overlay {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 10px;
}

.like-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50px;
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.like-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.05);
}

.like-btn.liked {
    background: rgba(220, 53, 69, 0.9);
    color: white;
}

.like-btn.liked:hover {
    background: rgba(220, 53, 69, 1);
}

.memory-content {
    padding: 1.5rem;
}

.memory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.memory-header .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;
    font-weight: 600;
}

.memory-header .user-info i {
    color: #d4af37;
    font-size: 1.2rem;
}

.memory-date {
    color: #6c757d;
    font-size: 0.85rem;
}

.memory-description {
    color: #495057;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.memory-actions {
    display: flex;
    gap: 10px;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.comment-btn {
    background: linear-gradient(45deg, #6c757d, #495057);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.comment-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

/* Comments Section */
.comments-section {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.comments-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.comment {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 10px;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
}

.comment-header i {
    color: #d4af37;
}

.comment-username {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.comment-date {
    color: #6c757d;
    font-size: 0.8rem;
    margin-right: auto;
}

.comment-text {
    color: #495057;
    line-height: 1.5;
    margin: 0;
}

.add-comment {
    display: flex;
    gap: 10px;
}

.add-comment textarea {
    flex: 1;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    resize: none;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.add-comment textarea:focus {
    outline: none;
    border-color: #d4af37;
}

.add-comment button {
    background: linear-gradient(45deg, #d4af37, #f1c40f);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.add-comment button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.notification-error {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.notification-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #212529;
}

.notification-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

/* No memories message */
.no-memories {
    text-align: center;
    color: #6c757d;
    font-size: 1.2rem;
    padding: 3rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    margin: 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .welcome-card {
        padding: 2rem 1rem;
    }

    .features {
        grid-template-columns: 1fr;
    }

    .auth-buttons {
        flex-direction: column;
        width: 100%;
    }

    .btn {
        width: 100%;
    }

    .memories-grid {
        grid-template-columns: 1fr;
    }

    .memory-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .add-comment {
        flex-direction: column;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
