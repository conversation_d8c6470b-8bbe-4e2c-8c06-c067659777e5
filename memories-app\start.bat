@echo off
echo ========================================
echo       تطبيق الذكريات - Memories App
echo ========================================
echo.

echo تحقق من تشغيل XAMPP...
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✓ Apache يعمل
) else (
    echo ✗ Apache لا يعمل - يرجى تشغيل XAMPP
    pause
    exit
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✓ MySQL يعمل
) else (
    echo ✗ MySQL لا يعمل - يرجى تشغيل XAMPP
    pause
    exit
)

echo.
echo فتح التطبيق في المتصفح...
echo.

REM Get the current directory name
for %%I in (.) do set CurrDirName=%%~nxI

REM Open the app in default browser
start http://localhost/%CurrDirName%/

echo.
echo ========================================
echo التطبيق يعمل الآن على:
echo http://localhost/%CurrDirName%/
echo.
echo للإعداد الأولي: http://localhost/%CurrDirName%/setup.php
echo للاختبار: http://localhost/%CurrDirName%/test.php
echo.
echo الحساب التجريبي:
echo اسم المستخدم: demo_user
echo كلمة المرور: demo123
echo ========================================
echo.
pause
