<?php
session_start();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق الذكريات - Memories App</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-camera-retro"></i>
                    <h1>تطبيق الذكريات</h1>
                </div>
                
                <?php if (isset($_SESSION['user_id'])): ?>
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <span class="username"><?php echo htmlspecialchars($_SESSION['username']); ?></span>
                        <a href="logout.php" class="logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="auth-buttons">
                        <a href="login.php" class="btn btn-primary">تسجيل الدخول</a>
                        <a href="register.php" class="btn btn-secondary">إنشاء حساب</a>
                    </div>
                <?php endif; ?>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <?php if (isset($_SESSION['user_id'])): ?>
                <!-- Upload Section -->
                <section class="upload-section">
                    <div class="upload-card">
                        <h2><i class="fas fa-cloud-upload-alt"></i> شارك ذكرياتك</h2>
                        <form id="uploadForm" action="upload.php" method="POST" enctype="multipart/form-data">
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-images"></i>
                                <p>اسحب الصور هنا أو انقر للاختيار</p>
                                <input type="file" id="fileInput" name="images[]" multiple accept="image/*" hidden>
                            </div>
                            <div class="form-group">
                                <label for="description">وصف الذكرى:</label>
                                <textarea id="description" name="description" placeholder="اكتب وصفاً جميلاً لهذه الذكرى..."></textarea>
                            </div>
                            <button type="submit" class="btn btn-upload">
                                <i class="fas fa-upload"></i> رفع الذكرى
                            </button>
                        </form>
                    </div>
                </section>

                <!-- Memories Gallery -->
                <section class="gallery-section">
                    <h2><i class="fas fa-photo-video"></i> معرض الذكريات</h2>
                    <div class="memories-grid" id="memoriesGrid">
                        <!-- Memories will be loaded here via AJAX -->
                    </div>
                </section>
            <?php else: ?>
                <!-- Welcome Section for Non-logged Users -->
                <section class="welcome-section">
                    <div class="welcome-card">
                        <div class="welcome-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h2>مرحباً بك في تطبيق الذكريات</h2>
                        <p>شارك أجمل لحظاتك واحتفالاتك ومناسباتك مع الأصدقاء والعائلة</p>
                        <div class="features">
                            <div class="feature">
                                <i class="fas fa-camera"></i>
                                <span>رفع الصور والذكريات</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-comments"></i>
                                <span>التعليق والتفاعل</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-globe"></i>
                                <span>وصول عالمي برمز QR</span>
                            </div>
                        </div>
                        <div class="auth-buttons">
                            <a href="register.php" class="btn btn-primary">ابدأ الآن</a>
                            <a href="login.php" class="btn btn-secondary">لديك حساب؟</a>
                        </div>
                    </div>
                </section>
            <?php endif; ?>
        </main>

        <!-- QR Code Section -->
        <section class="qr-section">
            <div class="qr-card">
                <h3><i class="fas fa-qrcode"></i> شارك التطبيق</h3>
                <div class="qr-code" id="qrCode">
                    <!-- QR Code will be generated here -->
                </div>
                <p>امسح الرمز للوصول للتطبيق من أي مكان في العالم</p>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 تطبيق الذكريات - جميع الحقوق محفوظة</p>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
</body>
</html>
