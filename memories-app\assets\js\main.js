// Main JavaScript for Memories App
document.addEventListener('DOMContentLoaded', function() {
    // Initialize QR Code
    generateQRCode();
    
    // Initialize upload functionality
    initializeUpload();
    
    // Load memories if user is logged in
    if (document.getElementById('memoriesGrid')) {
        loadMemories();
    }
    
    // Initialize smooth scrolling
    initializeSmoothScrolling();
});

// Generate QR Code for the app
function generateQRCode() {
    const qrContainer = document.getElementById('qrCode');
    if (qrContainer) {
        const currentURL = window.location.origin + window.location.pathname;

        // Clear any existing QR code
        qrContainer.innerHTML = '';

        // Generate new QR code
        QRCode.toCanvas(qrContainer, currentURL, {
            width: 200,
            height: 200,
            colorDark: '#2c3e50',
            colorLight: '#ffffff',
            margin: 2,
            errorCorrectionLevel: 'M'
        }, function (error) {
            if (error) {
                console.error('QR Code generation failed:', error);
                qrContainer.innerHTML = '<p>خطأ في إنتاج رمز QR</p>';
            } else {
                // Add QR code actions
                addQRCodeActions();
            }
        });
    }
}

// Add QR code action buttons
function addQRCodeActions() {
    const qrCard = document.querySelector('.qr-card');
    if (qrCard && !qrCard.querySelector('.qr-actions')) {
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'qr-actions';
        actionsDiv.innerHTML = `
            <button class="qr-btn" onclick="downloadQRCode()">
                <i class="fas fa-download"></i>
                تحميل
            </button>
            <button class="qr-btn" onclick="shareQRCode()">
                <i class="fas fa-share"></i>
                مشاركة
            </button>
        `;
        qrCard.appendChild(actionsDiv);
    }
}

// Download QR Code
function downloadQRCode() {
    const canvas = document.querySelector('#qrCode canvas');
    if (canvas) {
        const link = document.createElement('a');
        link.download = 'memories-app-qr-code.png';
        link.href = canvas.toDataURL();
        link.click();
        showNotification('تم تحميل رمز QR بنجاح', 'success');
    }
}

// Share QR Code
function shareQRCode() {
    const currentURL = window.location.origin + window.location.pathname;

    if (navigator.share) {
        navigator.share({
            title: 'تطبيق الذكريات',
            text: 'شارك ذكرياتك الجميلة معنا!',
            url: currentURL
        }).then(() => {
            showNotification('تم مشاركة الرابط بنجاح', 'success');
        }).catch((error) => {
            console.log('Error sharing:', error);
            copyToClipboard(currentURL);
        });
    } else {
        copyToClipboard(currentURL);
    }
}

// Copy URL to clipboard
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ الرابط إلى الحافظة', 'success');
        }).catch(() => {
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

// Fallback copy function
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showNotification('تم نسخ الرابط إلى الحافظة', 'success');
    } catch (err) {
        showNotification('فشل في نسخ الرابط', 'error');
    }

    document.body.removeChild(textArea);
}

// Initialize upload functionality
function initializeUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadForm = document.getElementById('uploadForm');
    
    if (uploadArea && fileInput) {
        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#f1c40f';
            uploadArea.style.backgroundColor = 'rgba(241, 196, 15, 0.1)';
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#d4af37';
            uploadArea.style.backgroundColor = '';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#d4af37';
            uploadArea.style.backgroundColor = '';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateUploadAreaText(files);
            }
        });
        
        // File input change
        fileInput.addEventListener('change', (e) => {
            updateUploadAreaText(e.target.files);
        });
    }
    
    // Form submission
    if (uploadForm) {
        uploadForm.addEventListener('submit', handleUploadSubmit);
    }
}

// Update upload area text when files are selected
function updateUploadAreaText(files) {
    const uploadArea = document.getElementById('uploadArea');
    const fileCount = files.length;
    
    if (fileCount > 0) {
        uploadArea.innerHTML = `
            <i class="fas fa-check-circle" style="color: #28a745;"></i>
            <p>تم اختيار ${fileCount} ${fileCount === 1 ? 'صورة' : 'صور'}</p>
        `;
    }
}

// Handle upload form submission
function handleUploadSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const submitBtn = e.target.querySelector('button[type="submit"]');
    
    // Show loading state
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الرفع...';
    submitBtn.disabled = true;
    
    fetch('upload.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم رفع الذكرى بنجاح!', 'success');
            e.target.reset();
            resetUploadArea();
            loadMemories(); // Reload memories
        } else {
            showNotification(data.message || 'حدث خطأ أثناء الرفع', 'error');
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        showNotification('حدث خطأ أثناء الرفع', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Reset upload area to initial state
function resetUploadArea() {
    const uploadArea = document.getElementById('uploadArea');
    if (uploadArea) {
        uploadArea.innerHTML = `
            <i class="fas fa-images"></i>
            <p>اسحب الصور هنا أو انقر للاختيار</p>
        `;
    }
}

// Load memories from server
function loadMemories() {
    const memoriesGrid = document.getElementById('memoriesGrid');
    if (!memoriesGrid) return;
    
    fetch('api/get_memories.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayMemories(data.memories);
        } else {
            memoriesGrid.innerHTML = '<p class="no-memories">لا توجد ذكريات بعد. ابدأ بإضافة أول ذكرى!</p>';
        }
    })
    .catch(error => {
        console.error('Error loading memories:', error);
        memoriesGrid.innerHTML = '<p class="error">خطأ في تحميل الذكريات</p>';
    });
}

// Display memories in the grid
function displayMemories(memories) {
    const memoriesGrid = document.getElementById('memoriesGrid');
    
    memoriesGrid.innerHTML = memories.map(memory => `
        <div class="memory-card" data-memory-id="${memory.id}">
            <div class="memory-image">
                <img src="${memory.image_path}" alt="${memory.title || 'ذكرى'}" loading="lazy">
                <div class="memory-overlay">
                    <button class="like-btn ${memory.user_liked ? 'liked' : ''}" onclick="toggleLike(${memory.id})">
                        <i class="fas fa-heart"></i>
                        <span class="like-count">${memory.likes_count || 0}</span>
                    </button>
                </div>
            </div>
            <div class="memory-content">
                <div class="memory-header">
                    <div class="user-info">
                        <i class="fas fa-user-circle"></i>
                        <span class="username">${memory.username}</span>
                    </div>
                    <span class="memory-date">${formatDate(memory.created_at)}</span>
                </div>
                ${memory.description ? `<p class="memory-description">${memory.description}</p>` : ''}
                <div class="memory-actions">
                    <button class="comment-btn" onclick="toggleComments(${memory.id})">
                        <i class="fas fa-comment"></i>
                        <span>التعليقات (${memory.comments_count || 0})</span>
                    </button>
                </div>
                <div class="comments-section" id="comments-${memory.id}" style="display: none;">
                    <div class="comments-list" id="comments-list-${memory.id}">
                        <!-- Comments will be loaded here -->
                    </div>
                    <div class="add-comment">
                        <textarea placeholder="اكتب تعليقك..." id="comment-input-${memory.id}"></textarea>
                        <button onclick="addComment(${memory.id})">إضافة تعليق</button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Toggle like for a memory
function toggleLike(memoryId) {
    fetch('api/toggle_like.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ memory_id: memoryId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const likeBtn = document.querySelector(`[data-memory-id="${memoryId}"] .like-btn`);
            const likeCount = likeBtn.querySelector('.like-count');
            
            if (data.liked) {
                likeBtn.classList.add('liked');
            } else {
                likeBtn.classList.remove('liked');
            }
            
            likeCount.textContent = data.likes_count;
        }
    })
    .catch(error => console.error('Error toggling like:', error));
}

// Toggle comments section
function toggleComments(memoryId) {
    const commentsSection = document.getElementById(`comments-${memoryId}`);
    
    if (commentsSection.style.display === 'none') {
        commentsSection.style.display = 'block';
        loadComments(memoryId);
    } else {
        commentsSection.style.display = 'none';
    }
}

// Load comments for a memory
function loadComments(memoryId) {
    fetch(`api/get_comments.php?memory_id=${memoryId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const commentsList = document.getElementById(`comments-list-${memoryId}`);
            commentsList.innerHTML = data.comments.map(comment => `
                <div class="comment">
                    <div class="comment-header">
                        <i class="fas fa-user-circle"></i>
                        <span class="comment-username">${comment.username}</span>
                        <span class="comment-date">${formatDate(comment.created_at)}</span>
                    </div>
                    <p class="comment-text">${comment.comment}</p>
                </div>
            `).join('');
        }
    })
    .catch(error => console.error('Error loading comments:', error));
}

// Add a new comment
function addComment(memoryId) {
    const commentInput = document.getElementById(`comment-input-${memoryId}`);
    const commentText = commentInput.value.trim();
    
    if (!commentText) {
        showNotification('يرجى كتابة تعليق', 'warning');
        return;
    }
    
    fetch('api/add_comment.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            memory_id: memoryId,
            comment: commentText
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            commentInput.value = '';
            loadComments(memoryId);
            showNotification('تم إضافة التعليق بنجاح', 'success');
        } else {
            showNotification(data.message || 'خطأ في إضافة التعليق', 'error');
        }
    })
    .catch(error => {
        console.error('Error adding comment:', error);
        showNotification('خطأ في إضافة التعليق', 'error');
    });
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
        return 'أمس';
    } else if (diffDays < 7) {
        return `منذ ${diffDays} أيام`;
    } else {
        return date.toLocaleDateString('ar-SA');
    }
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Initialize smooth scrolling
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}
