# تطبيق الذكريات - Memories App

تطبيق ويب جميل لمشاركة الذكريات والصور مع الأصدقاء والعائلة، مصمم بطابع كلاسيكي جذاب ومتاح عالمياً عبر رمز QR.

## المميزات الرئيسية

### 🔐 نظام المستخدمين
- تسجيل حساب جديد بسهولة
- تسجيل دخول آمن
- حساب تجريبي للاختبار (اسم المستخدم: `demo_user`، كلمة المرور: `demo123`)
- عرض معلومات المستخدم مع أيقونة مخصصة

### 📸 إدارة الذكريات
- رفع الصور بسهولة (سحب وإفلات أو النقر للاختيار)
- دعم تنسيقات متعددة: JPEG, PNG, GIF, WebP
- ضغط تلقائي للصور لتوفير المساحة
- إضافة وصف للذكريات
- عرض الذكريات في معرض جذاب

### 💬 التفاعل الاجتماعي
- نظام الإعجاب (Like) للذكريات
- التعليق على الذكريات
- عرض اسم المستخدم ورمزه مع كل تعليق
- عداد الإعجابات والتعليقات

### 📱 رمز QR للوصول العالمي
- إنتاج رمز QR تلقائياً للتطبيق
- إمكانية تحميل رمز QR
- مشاركة الرابط بسهولة
- وصول من أي مكان في العالم

### 🎨 التصميم الكلاسيكي
- واجهة مستخدم جذابة بطابع كلاسيكي
- تدرجات لونية جميلة
- تأثيرات بصرية متقدمة
- تصميم متجاوب يعمل على جميع الأجهزة
- خطوط عربية أنيقة (Cairo & Amiri)

## متطلبات التشغيل

### الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache مع mod_rewrite
- مساحة تخزين كافية للصور

### المتصفح
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## التثبيت والإعداد

### 1. نسخ الملفات
```bash
# نسخ مجلد التطبيق إلى مجلد الخادم
cp -r memories-app /path/to/your/webserver/
```

### 2. إعداد قاعدة البيانات
- قم بإنشاء قاعدة بيانات MySQL جديدة
- عدّل إعدادات قاعدة البيانات في `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'memories_app');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلد الرفع
chmod 755 uploads/
chmod 755 config/
```

### 4. تشغيل التطبيق
- افتح المتصفح وانتقل إلى رابط التطبيق
- سيتم إنشاء قاعدة البيانات والجداول تلقائياً في أول زيارة

## هيكل المشروع

```
memories-app/
├── index.php              # الصفحة الرئيسية
├── login.php              # صفحة تسجيل الدخول
├── register.php           # صفحة إنشاء حساب
├── logout.php             # تسجيل الخروج
├── upload.php             # رفع الصور
├── 404.php                # صفحة الخطأ 404
├── .htaccess              # إعدادات Apache
├── README.md              # هذا الملف
├── config/
│   └── database.php       # إعدادات قاعدة البيانات
├── api/
│   ├── get_memories.php   # جلب الذكريات
│   ├── toggle_like.php    # تبديل الإعجاب
│   ├── get_comments.php   # جلب التعليقات
│   ├── add_comment.php    # إضافة تعليق
│   └── create_demo_user.php # إنشاء المستخدم التجريبي
├── assets/
│   ├── css/
│   │   ├── style.css      # التصميم الرئيسي
│   │   └── auth.css       # تصميم صفحات المصادقة
│   └── js/
│       └── main.js        # JavaScript الرئيسي
└── uploads/
    └── memories/          # مجلد الصور المرفوعة
```

## الاستخدام

### للمستخدمين الجدد
1. انقر على "إنشاء حساب جديد"
2. أدخل اسم المستخدم وكلمة المرور
3. ابدأ في رفع ذكرياتك الجميلة

### للاختبار السريع
- استخدم الحساب التجريبي:
  - اسم المستخدم: `demo_user`
  - كلمة المرور: `demo123`

### رفع الذكريات
1. انقر على منطقة الرفع أو اسحب الصور إليها
2. أضف وصفاً للذكرى (اختياري)
3. انقر على "رفع الذكرى"

### التفاعل مع الذكريات
- انقر على أيقونة القلب للإعجاب
- انقر على "التعليقات" لعرض أو إضافة تعليق
- اكتب تعليقك وانقر "إضافة تعليق"

## الأمان

- حماية من هجمات XSS و CSRF
- تشفير كلمات المرور باستخدام PHP password_hash
- فلترة وتنظيف جميع المدخلات
- حماية الملفات الحساسة
- تحديد أنواع الملفات المسموحة للرفع

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من تشغيل PHP و MySQL
2. تحقق من صلاحيات الملفات
3. راجع سجلات الأخطاء في الخادم
4. تأكد من إعدادات قاعدة البيانات

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**تطبيق الذكريات** - حيث تحيا الذكريات الجميلة 💝
