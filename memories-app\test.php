<?php
/**
 * Test Script for Memories App
 * This script tests various functionalities
 */

session_start();

// Only allow access if user is logged in or in development mode
$dev_mode = true; // Set to false in production

if (!$dev_mode && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

require_once 'config/database.php';

$tests = [];
$overall_status = true;

// Test 1: Database Connection
try {
    $database = new Database();
    $db = $database->getConnection();
    $tests['Database Connection'] = ['status' => true, 'message' => 'Connected successfully'];
} catch (Exception $e) {
    $tests['Database Connection'] = ['status' => false, 'message' => $e->getMessage()];
    $overall_status = false;
}

// Test 2: Check Tables
if (isset($db)) {
    $required_tables = ['users', 'memories', 'comments', 'likes'];
    foreach ($required_tables as $table) {
        try {
            $stmt = $db->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $tests["Table: $table"] = ['status' => true, 'message' => 'Table exists'];
            } else {
                $tests["Table: $table"] = ['status' => false, 'message' => 'Table missing'];
                $overall_status = false;
            }
        } catch (Exception $e) {
            $tests["Table: $table"] = ['status' => false, 'message' => $e->getMessage()];
            $overall_status = false;
        }
    }
}

// Test 3: File Permissions
$directories = [
    'uploads/' => 'Uploads directory',
    'config/' => 'Config directory'
];

foreach ($directories as $dir => $name) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            $tests[$name] = ['status' => true, 'message' => 'Directory writable'];
        } else {
            $tests[$name] = ['status' => false, 'message' => 'Directory not writable'];
            $overall_status = false;
        }
    } else {
        $tests[$name] = ['status' => false, 'message' => 'Directory does not exist'];
        $overall_status = false;
    }
}

// Test 4: PHP Extensions
$required_extensions = [
    'pdo' => 'PDO Extension',
    'pdo_mysql' => 'PDO MySQL Extension',
    'gd' => 'GD Extension',
    'json' => 'JSON Extension'
];

foreach ($required_extensions as $ext => $name) {
    if (extension_loaded($ext)) {
        $tests[$name] = ['status' => true, 'message' => 'Extension loaded'];
    } else {
        $tests[$name] = ['status' => false, 'message' => 'Extension not loaded'];
        $overall_status = false;
    }
}

// Test 5: Demo User
if (isset($db)) {
    try {
        $stmt = $db->prepare("SELECT id FROM users WHERE username = 'demo_user'");
        $stmt->execute();
        if ($stmt->fetch()) {
            $tests['Demo User'] = ['status' => true, 'message' => 'Demo user exists'];
        } else {
            $tests['Demo User'] = ['status' => false, 'message' => 'Demo user not found'];
        }
    } catch (Exception $e) {
        $tests['Demo User'] = ['status' => false, 'message' => $e->getMessage()];
    }
}

// Test 6: Upload Functionality
$upload_test = false;
if (is_writable('uploads/')) {
    $test_file = 'uploads/test_' . time() . '.txt';
    if (file_put_contents($test_file, 'test')) {
        if (unlink($test_file)) {
            $tests['File Upload'] = ['status' => true, 'message' => 'Upload functionality working'];
            $upload_test = true;
        }
    }
}

if (!$upload_test) {
    $tests['File Upload'] = ['status' => false, 'message' => 'Upload functionality not working'];
    $overall_status = false;
}

// Test 7: Session Functionality
if (session_status() === PHP_SESSION_ACTIVE) {
    $tests['Session Support'] = ['status' => true, 'message' => 'Sessions working'];
} else {
    $tests['Session Support'] = ['status' => false, 'message' => 'Sessions not working'];
    $overall_status = false;
}

// Test 8: API Endpoints
$api_endpoints = [
    'api/get_memories.php' => 'Get Memories API',
    'api/toggle_like.php' => 'Toggle Like API',
    'api/get_comments.php' => 'Get Comments API',
    'api/add_comment.php' => 'Add Comment API'
];

foreach ($api_endpoints as $endpoint => $name) {
    if (file_exists($endpoint)) {
        $tests[$name] = ['status' => true, 'message' => 'API file exists'];
    } else {
        $tests[$name] = ['status' => false, 'message' => 'API file missing'];
        $overall_status = false;
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق - تطبيق الذكريات</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid #d4af37;
            margin-bottom: 2rem;
        }
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .test-status {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        .test-status.pass {
            background: #28a745;
            color: white;
        }
        .test-status.fail {
            background: #dc3545;
            color: white;
        }
        .test-list {
            list-style: none;
            padding: 0;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        .test-item.pass {
            background: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
        }
        .test-item.fail {
            background: rgba(220, 53, 69, 0.1);
            border-color: #dc3545;
        }
        .test-name {
            font-weight: 600;
        }
        .test-result {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-icon {
            font-size: 1.2rem;
        }
        .test-icon.pass {
            color: #28a745;
        }
        .test-icon.fail {
            color: #dc3545;
        }
        .test-message {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="test-card">
                <div class="test-header">
                    <div class="auth-logo">
                        <i class="fas fa-vial"></i>
                        <h1>اختبار تطبيق الذكريات</h1>
                    </div>
                    <div class="test-status <?php echo $overall_status ? 'pass' : 'fail'; ?>">
                        <i class="fas fa-<?php echo $overall_status ? 'check-circle' : 'times-circle'; ?>"></i>
                        <?php echo $overall_status ? 'جميع الاختبارات نجحت' : 'بعض الاختبارات فشلت'; ?>
                    </div>
                </div>
                
                <ul class="test-list">
                    <?php foreach ($tests as $test_name => $test_result): ?>
                        <li class="test-item <?php echo $test_result['status'] ? 'pass' : 'fail'; ?>">
                            <div>
                                <div class="test-name"><?php echo htmlspecialchars($test_name); ?></div>
                                <div class="test-message"><?php echo htmlspecialchars($test_result['message']); ?></div>
                            </div>
                            <div class="test-result">
                                <i class="fas fa-<?php echo $test_result['status'] ? 'check' : 'times'; ?> test-icon <?php echo $test_result['status'] ? 'pass' : 'fail'; ?>"></i>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
                
                <div class="actions">
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        العودة للرئيسية
                    </a>
                    <button onclick="location.reload()" class="btn btn-secondary">
                        <i class="fas fa-redo"></i>
                        إعادة الاختبار
                    </button>
                </div>
            </div>
            
            <?php if (!$overall_status): ?>
                <div class="test-card">
                    <h3><i class="fas fa-exclamation-triangle"></i> إرشادات حل المشاكل</h3>
                    <ul>
                        <li>تأكد من تشغيل خادم MySQL</li>
                        <li>تحقق من إعدادات قاعدة البيانات في config/database.php</li>
                        <li>تأكد من صلاحيات الكتابة لمجلدات uploads و config</li>
                        <li>تحقق من تثبيت جميع امتدادات PHP المطلوبة</li>
                        <li>قم بتشغيل setup.php إذا لم تقم بذلك بعد</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
