<?php
/**
 * Setup Script for Memories App
 * This script helps with initial setup and testing
 */

// Check if setup is already done
if (file_exists('config/.setup_complete')) {
    header('Location: index.php');
    exit();
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

// Step 1: Check requirements
if ($step == 1) {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'GD Extension' => extension_loaded('gd'),
        'JSON Extension' => extension_loaded('json'),
        'Session Support' => function_exists('session_start'),
        'File Upload Support' => ini_get('file_uploads'),
        'Config Directory Writable' => is_writable('config/'),
        'Uploads Directory Exists' => is_dir('uploads/') || mkdir('uploads/', 0755, true),
        'Uploads Directory Writable' => is_writable('uploads/') || chmod('uploads/', 0755)
    ];
}

// Step 2: Database setup
if ($step == 2 && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'memories_app';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    
    try {
        // Test connection
        $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$db_name`");
        
        // Update config file
        $config_content = "<?php\n";
        $config_content .= "// Database configuration\n";
        $config_content .= "define('DB_HOST', '$db_host');\n";
        $config_content .= "define('DB_NAME', '$db_name');\n";
        $config_content .= "define('DB_USER', '$db_user');\n";
        $config_content .= "define('DB_PASS', '$db_pass');\n\n";
        
        // Add the rest of the database.php content
        $config_content .= file_get_contents('config/database.php');
        $config_content = preg_replace('/^<\?php.*?define\(\'DB_PASS\', \'.*?\'\);/s', '', $config_content);
        
        file_put_contents('config/database.php', $config_content);
        
        $success[] = 'Database connection successful!';
        $step = 3;
        
    } catch (PDOException $e) {
        $errors[] = 'Database connection failed: ' . $e->getMessage();
    }
}

// Step 3: Create admin user
if ($step == 3 && $_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'config/database.php';
    
    $admin_username = $_POST['admin_username'] ?? '';
    $admin_password = $_POST['admin_password'] ?? '';
    $admin_email = $_POST['admin_email'] ?? '';
    
    if (empty($admin_username) || empty($admin_password)) {
        $errors[] = 'Username and password are required';
    } else {
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            // Check if user already exists
            $stmt = $db->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$admin_username]);
            
            if ($stmt->fetch()) {
                $errors[] = 'Username already exists';
            } else {
                $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                $stmt = $db->prepare("INSERT INTO users (username, password, email) VALUES (?, ?, ?)");
                
                if ($stmt->execute([$admin_username, $hashed_password, $admin_email])) {
                    $success[] = 'Admin user created successfully!';
                    
                    // Mark setup as complete
                    file_put_contents('config/.setup_complete', date('Y-m-d H:i:s'));
                    $step = 4;
                } else {
                    $errors[] = 'Failed to create admin user';
                }
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد تطبيق الذكريات</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .setup-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .setup-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid #d4af37;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #d4af37;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .requirements-list {
            list-style: none;
            padding: 0;
        }
        .requirements-list li {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .requirements-list li.pass {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }
        .requirements-list li.fail {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
    </style>
</head>
<body class="auth-body">
    <div class="setup-container">
        <div class="setup-card">
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-cog"></i>
                    <h1>إعداد تطبيق الذكريات</h1>
                </div>
            </div>
            
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step == 1 ? 'active' : 'completed') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step == 2 ? 'active' : 'completed') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step == 3 ? 'active' : 'completed') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? 'active' : ''; ?>">4</div>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <?php foreach ($errors as $error): ?>
                        <p><i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php foreach ($success as $msg): ?>
                        <p><i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($msg); ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <h2>فحص المتطلبات</h2>
                <ul class="requirements-list">
                    <?php foreach ($requirements as $req => $status): ?>
                        <li class="<?php echo $status ? 'pass' : 'fail'; ?>">
                            <span><?php echo $req; ?></span>
                            <i class="fas fa-<?php echo $status ? 'check' : 'times'; ?>"></i>
                        </li>
                    <?php endforeach; ?>
                </ul>
                
                <?php if (array_product($requirements)): ?>
                    <a href="?step=2" class="btn btn-primary btn-full">
                        <i class="fas fa-arrow-left"></i> التالي: إعداد قاعدة البيانات
                    </a>
                <?php else: ?>
                    <p class="alert alert-error">يرجى حل المشاكل أعلاه قبل المتابعة</p>
                <?php endif; ?>
                
            <?php elseif ($step == 2): ?>
                <h2>إعداد قاعدة البيانات</h2>
                <form method="POST">
                    <div class="form-group">
                        <label>خادم قاعدة البيانات</label>
                        <input type="text" name="db_host" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label>اسم قاعدة البيانات</label>
                        <input type="text" name="db_name" value="memories_app" required>
                    </div>
                    <div class="form-group">
                        <label>اسم المستخدم</label>
                        <input type="text" name="db_user" value="root" required>
                    </div>
                    <div class="form-group">
                        <label>كلمة المرور</label>
                        <input type="password" name="db_pass">
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-database"></i> اختبار الاتصال وإنشاء قاعدة البيانات
                    </button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <h2>إنشاء حساب المدير</h2>
                <form method="POST">
                    <div class="form-group">
                        <label>اسم المستخدم</label>
                        <input type="text" name="admin_username" required>
                    </div>
                    <div class="form-group">
                        <label>كلمة المرور</label>
                        <input type="password" name="admin_password" required>
                    </div>
                    <div class="form-group">
                        <label>البريد الإلكتروني (اختياري)</label>
                        <input type="email" name="admin_email">
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-user-plus"></i> إنشاء حساب المدير
                    </button>
                </form>
                
            <?php elseif ($step == 4): ?>
                <h2>تم الإعداد بنجاح!</h2>
                <div class="alert alert-success">
                    <p><i class="fas fa-check-circle"></i> تم إعداد تطبيق الذكريات بنجاح!</p>
                </div>
                <p>يمكنك الآن استخدام التطبيق والبدء في مشاركة ذكرياتك الجميلة.</p>
                <a href="index.php" class="btn btn-primary btn-full">
                    <i class="fas fa-home"></i> الذهاب إلى التطبيق
                </a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
