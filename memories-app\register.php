<?php
session_start();
require_once 'config/database.php';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $email = trim($_POST['email']);
    
    // Validation
    if (empty($username) || empty($password) || empty($confirm_password)) {
        $error = 'جميع الحقول مطلوبة';
    } elseif (strlen($username) < 3) {
        $error = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمات المرور غير متطابقة';
    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            // Check if username already exists
            $stmt = $db->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            
            if ($stmt->fetch()) {
                $error = 'اسم المستخدم موجود بالفعل';
            } else {
                // Check if email already exists (if provided)
                if (!empty($email)) {
                    $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
                    $stmt->execute([$email]);
                    
                    if ($stmt->fetch()) {
                        $error = 'البريد الإلكتروني موجود بالفعل';
                    }
                }
                
                if (empty($error)) {
                    // Create new user
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $db->prepare("INSERT INTO users (username, password, email) VALUES (?, ?, ?)");
                    
                    if ($stmt->execute([$username, $hashed_password, $email])) {
                        $success = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                    } else {
                        $error = 'حدث خطأ أثناء إنشاء الحساب';
                    }
                }
            }
        } catch (PDOException $e) {
            $error = 'خطأ في قاعدة البيانات';
            error_log($e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب - تطبيق الذكريات</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-camera-retro"></i>
                    <h1>تطبيق الذكريات</h1>
                </div>
                <h2>إنشاء حساب جديد</h2>
                <p>انضم إلينا وابدأ في مشاركة ذكرياتك الجميلة</p>
            </div>
            
            <div class="auth-form">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <div class="form-group">
                        <label for="username">
                            <i class="fas fa-user"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" id="username" name="username" required 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                               placeholder="اختر اسم مستخدم فريد">
                    </div>
                    
                    <div class="form-group">
                        <label for="email">
                            <i class="fas fa-envelope"></i>
                            البريد الإلكتروني (اختياري)
                        </label>
                        <input type="email" id="email" name="email" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <input type="password" id="password" name="password" required 
                               placeholder="اختر كلمة مرور قوية">
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">
                            <i class="fas fa-lock"></i>
                            تأكيد كلمة المرور
                        </label>
                        <input type="password" id="confirm_password" name="confirm_password" required 
                               placeholder="أعد كتابة كلمة المرور">
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-user-plus"></i>
                        إنشاء الحساب
                    </button>
                </form>
            </div>
            
            <div class="auth-footer">
                <p>لديك حساب بالفعل؟ <a href="login.php">سجل الدخول</a></p>
                <p><a href="index.php">العودة للصفحة الرئيسية</a></p>
            </div>
        </div>
        
        <!-- Decorative Elements -->
        <div class="auth-decoration">
            <div class="floating-icon" style="top: 10%; left: 10%;">
                <i class="fas fa-heart"></i>
            </div>
            <div class="floating-icon" style="top: 20%; right: 15%;">
                <i class="fas fa-camera"></i>
            </div>
            <div class="floating-icon" style="bottom: 30%; left: 20%;">
                <i class="fas fa-star"></i>
            </div>
            <div class="floating-icon" style="bottom: 20%; right: 10%;">
                <i class="fas fa-gift"></i>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Floating animation for decorative icons
            const floatingIcons = document.querySelectorAll('.floating-icon');
            floatingIcons.forEach((icon, index) => {
                icon.style.animationDelay = `${index * 0.5}s`;
            });
            
            // Form validation feedback
            const form = document.querySelector('form');
            const inputs = form.querySelectorAll('input');
            
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() !== '') {
                        this.classList.add('has-value');
                    } else {
                        this.classList.remove('has-value');
                    }
                });
            });
            
            // Password confirmation validation
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm_password');
            
            confirmPassword.addEventListener('input', function() {
                if (password.value !== this.value) {
                    this.setCustomValidity('كلمات المرور غير متطابقة');
                } else {
                    this.setCustomValidity('');
                }
            });
        });
    </script>
</body>
</html>
